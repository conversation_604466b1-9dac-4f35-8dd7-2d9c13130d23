<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <LangVersion>9.0</LangVersion>
    <_TargetFrameworkDirectories>non_empty_path_generated_by_unity.rider.package</_TargetFrameworkDirectories>
    <_FullFrameworkReferenceAssemblyPaths>non_empty_path_generated_by_unity.rider.package</_FullFrameworkReferenceAssemblyPaths>
    <DisableHandlePackageFileConflicts>true</DisableHandlePackageFileConflicts>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{9999d1d4-48d8-a68e-d38f-533843d0f468}</ProjectGuid>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\Bin\Debug\Assembly-CSharp\</OutputPath>
    <DefineConstants>UNITY_6000_1_11;UNITY_6000_1;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_6000_1_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_STANDALONE_LINUX;PLATFORM_STANDALONE;UNITY_STANDALONE_LINUX;UNITY_STANDALONE;UNITY_STANDALONE_LINUX_API;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;ENABLE_SPATIALTRACKING;ENABLE_MODULAR_UNITYENGINE_ASSEMBLIES;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_LINUX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169,0649</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets/Thirdparty/PSXShaderKit/Scripts/PSXShaderManager.cs" />
    <Compile Include="Assets/_Scripts/Managers/Camera/MoveCamera.cs" />
    <Compile Include="Assets/Thirdparty/PSXShaderKit/Demo/PSXExample_CameraMovement.cs" />
    <Compile Include="Assets/InputSystem_Actions.cs" />
    <Compile Include="Assets/TutorialInfo/Scripts/Readme.cs" />
    <Compile Include="Assets/Thirdparty/PSXShaderKit/Scripts/PSXPostProcessEffect.cs" />
    <Compile Include="Assets/Thirdparty/PSXShaderKit/Demo/SpookyOrbFloat.cs" />
    <Compile Include="Assets/_Scripts/Units/Player/PlayerMovement.cs" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/!Default.json" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-VertexLitTransparentCutout.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-UnlitTransparentCutout.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Lite-UnlitTransparentCutout.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Electric Blue.json" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/ImageEffects/PSX-Pixelation.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/ImageEffects/PSX-Interlacing.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Subtle Breeze.json" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-ShaderSrc-Lite.cginc" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Utils.cginc" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Inverted.json" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-VertexLit.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Noir Purple.json" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/ImageEffects/PSX-Dithering.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/ImageEffects/PSX-PostProcess.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Lite-VertexLitTransparentCutout.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Lite-UnlitTransparent.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-VertexLitTransparent.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-ShaderSrc.cginc" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Unlit Transparent.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Glacier.json" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Solid Gold.json" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Unlit.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-UnlitTransparent.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Lite-VertexLit.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/ImageEffects/PSX-PostProcess-Accurate.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Earthy Light.json" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Shaders/CelNoOutline.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Shaders/CelSilhouette.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Shaders/CelOutline.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Lite-Unlit.shader" />
    <None Include="Assets/Thirdparty/PSXShaderKit/Shaders/PSX-Lite-VertexLitTransparent.shader" />
    <None Include="Assets/Thirdparty/FlexibleCelShader/Presets/Summer Evening.json" />
    <Reference Include="UnityEngine">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/PackageCache/com.unity.ext.nunit@b16e6d09cd93/net40/unity-custom/nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/PackageCache/com.unity.visualscripting@6279e2b7c485/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll</HintPath>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll</HintPath>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll</HintPath>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll</HintPath>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll</HintPath>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll</HintPath>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/home/<USER>/Unity/Hub/Editor/6000.1.11f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Searcher.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Rider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.AI.Navigation.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Timeline.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Timeline.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Mathematics.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Collections.dll</HintPath>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/PPv2URPConverters.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.Core.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Collections.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Updater">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.AI.Navigation.Updater.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Mathematics.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.State.dll</HintPath>
    </Reference>
    <Reference Include="Unity.AI.Navigation.Editor.ConversionSystem">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.AI.Navigation.Editor.ConversionSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Burst.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Burst.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>/home/<USER>/Documents/Projects/movement-rhythm-test/Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
